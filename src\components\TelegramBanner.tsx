
import React, { useState, useEffect } from "react";

// Telegram channel link
const telegramLink = "https://t.me/thestreamdb";

// Multiple image sources for better compatibility
const telegramImageSources = [
  "/telegram-banner.png",
  "/telegram-banner-backup.png",
  "./telegram-banner.png",
  "/public/telegram-banner.png"
];

export default function TelegramBanner() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showFallback, setShowFallback] = useState(false);

  // Preload image to check if it exists
  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setImageLoaded(true);
      setShowFallback(false);
    };
    img.onerror = () => {
      if (currentImageIndex < telegramImageSources.length - 1) {
        setCurrentImageIndex(prev => prev + 1);
      } else {
        setShowFallback(true);
        setImageLoaded(false);
      }
    };
    img.src = telegramImageSources[currentImageIndex];
  }, [currentImageIndex]);

  return (
    <section
      className="mb-4 mx-auto max-w-3xl"
      style={{
        background: "none",
        boxShadow: "none",
        border: "none",
        padding: 0,
        transform: "scale(1)",
      }}
    >
      {/* Match Cloudflare banner structure exactly */}
      <div
        className="relative rounded-xl w-full mx-auto overflow-hidden"
        style={{ minHeight: 110 }}
      >
        <div
          className="relative z-10 px-2 py-5 flex flex-col items-center justify-center"
          style={{
            minHeight: 150, // Match Cloudflare banner's actual content height
          }}
        >
          {/* Clickable area for the entire content */}
          <a
            href={telegramLink}
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-background rounded-xl"
            aria-label="Join our Telegram channel for latest uploads, exclusive content, and early access"
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                window.open(telegramLink, '_blank', 'noopener,noreferrer');
              }
            }}
          >
            {/* Telegram Banner Content */}
            {!showFallback && (
              <img
                src={telegramImageSources[currentImageIndex]}
                alt="Join our Telegram channel for latest uploads, exclusive content, and early access"
                className="w-full rounded-xl transition-transform duration-200 group-hover:scale-[1.02]"
                style={{
                  height: "150px", // Match content area minHeight for proper card height
                  width: "100%",
                  objectFit: "cover", // Full coverage without black space or gaps
                  objectPosition: "center",
                  display: imageLoaded ? "block" : "none",
                  background: "linear-gradient(to right, #2fbdfa 0%, #2da1fa 100%)", // Fallback gradient background
                }}
                loading="lazy"
                crossOrigin="anonymous"
                onLoad={(e) => {
                  const target = e.target as HTMLImageElement;
                  setImageLoaded(true);
                  setShowFallback(false);
                  console.log('Telegram banner loaded successfully:', target.src);
                }}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  console.error('Telegram banner failed to load:', target.src);

                  if (currentImageIndex < telegramImageSources.length - 1) {
                    setCurrentImageIndex(prev => prev + 1);
                  } else {
                    setShowFallback(true);
                    setImageLoaded(false);
                  }
                }}
              />
            )}

            {/* Fallback Banner */}
            {(showFallback || !imageLoaded) && (
              <div
                className="w-full rounded-xl transition-transform duration-200 group-hover:scale-[1.02]"
                style={{
                  background: "linear-gradient(to right, #2fbdfa 0%, #2da1fa 100%)",
                  color: "white",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "150px",
                  width: "100%",
                  fontFamily: "system-ui, -apple-system, sans-serif",
                  fontSize: "1.2rem",
                  fontWeight: "bold",
                  textAlign: "center",
                  padding: "1rem",
                  flexDirection: "column",
                  gap: "0.5rem"
                }}
              >
                <div style={{ fontSize: "2rem" }}>📱</div>
                <div>Join our Telegram Channel</div>
                <div style={{ fontSize: "0.9rem", opacity: 0.9 }}>
                  Latest uploads & exclusive content
                </div>
              </div>
            )}
          </a>
        </div>
      </div>
    </section>
  );
}
